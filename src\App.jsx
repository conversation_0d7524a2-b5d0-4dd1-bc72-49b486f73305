import { useState } from "react";
import { Routes, Route } from "react-router-dom";
import { LanguageProvider } from "../context/LanguageContext";
import "./App.css";
import Home from "../pages/Hero";
import SignUp from "../pages/Signup";
import Login from "../pages/Login";
import MicTest from "../pages/Mictest";
import Main from "../pages/Main";
import ContactPage from "../pages/ContactPage";
import NotesViewer from "../pages/NotesViewer";
import UploadPdfComponent from "../pages/UploadPdfComponent";
import RewardsCenter from "../pages/RewardsCenter";
import StudyPlanJS from "../pages/StudyPlanJS";
import ContestMain from "./../pages/ContestMain";
import QuizLoading from "./../pages/QuizLoading";
import QuizInterface from "./../pages/QuizInterface";
import QuizQuestion from "./../pages/QuizQuestion";
import QuizResults from "./../pages/Results";
import LandingPage from "../pages/Landingpage";
import LoadingPage from "../pages/Loading";
import AiInterview from "../pages/aiInterview";
import Newsignup from "../pages/Sign";
import StudentType from "../pages/student-type";
import ClassStream from "../pages/class-stream";
import EducationDetails from "../pages/education-details";
import Dashboard from "../pages/dashboard";
import Loading from "../pages/Loader"
import ExamIntro from "../pages/exam-mcq";
import McqExam from "../pages/examQuestion";
import McqHistory from "../pages/Exam-solute";
import Notes from "../pages/Notes";
import Planner from "../pages/Dash";
import DisclaimerPage from "../pages/DisclaimerPage";
import ExamToggle from "../pages/Examtoggle";
import Avatar from "../pages/Avatar";
import TermsAndConditions from "../pages/TermsPage";
import RefundPolicy from "../pages/RefundPage";
import PrivacyPage from "../pages/PrivacyPage"
import B2Bconnect from "../pages/BtoBconnector"
import AIResourceFinder from "../pages/AIResourceFinder(0)";
import SetSchedule from "../pages/SetSchedule";
import SmartResources from "../pages/SmartResources";
import StructuredBreakdown from "../pages/StructuredBreakdown";
import SetSchedule2 from "../pages/SetSchedule2";
import VideoSection from "../pages/VideoSection";
import QuestionUploadComponent from "../pages/CopyCheck";
import HandwrittenTest from "../pages/HandwrittenTest";
import AIFinder from "../pages/AIFinder";
import ResourceNotes from "../pages/resourceNotes";
import ContestPopup from "../pages/LockPopUp.jsx";
import StructuredBreakdown2 from "../pages/StructuredBreakdown2";
import Viva from "../pages/viva";
import NotesPdf from "../pages/NotesPdf";



export default function App() {
  return (
    <LanguageProvider>
      <Routes>
        <Route path="/" element={<Main />} />
        <Route path="/signup" element={<SignUp />} />
        <Route path="/loading" element={<LoadingPage />} />
        <Route path="/login" element={<Login />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/upload-page" element={<UploadPdfComponent />} />
        <Route path="/ask-doubt" element={<NotesViewer />} />
        <Route path="/rewards" element={<RewardsCenter />} />
        <Route path="/commercial" element={<LandingPage />} />
        <Route path="/study-plan" element={<StudyPlanJS />} />
        <Route path="/contest" element={<ContestMain />} />
        <Route path="/quiz-loading" element={<QuizLoading />} />
        <Route path="/quiz-waiting-interface" element={<QuizInterface />} />
        <Route path="/quiz-question" element={<QuizQuestion />} />
        <Route path="/quiz-results" element={<QuizResults />} />
        <Route path="/aiInterview" element={<AiInterview />} />
        <Route path="/newsignup" element={<Newsignup />} />
        <Route path="/education-details" element={<EducationDetails />} />
        <Route path="/student-type" element={<StudentType />} />
        <Route path="/class-stream" element={<ClassStream />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/loading-sequence" element={<Loading />} />
        <Route path="/exam-intro" element={<ExamIntro />} />
        <Route path="/mcq-exam-quiz" element={<McqExam />} />
        <Route path="/mcq-history" element={<McqHistory />} />
        <Route path="/notes" element={<Notes />} />
        <Route path="/ogdashboard" element={<Planner />} />
        <Route path="/disclaimer" element={<DisclaimerPage />} />
        <Route path="/exam-toggle" element={<ExamToggle />} />
        <Route path="/avatar" element={<Avatar />} />
        <Route path="/mic" element={<MicTest />} />
        <Route path="/terms" element={<TermsAndConditions />} />
        <Route path="/refund" element={<RefundPolicy />} />
        <Route path="/privacy" element={<PrivacyPage />} />
        <Route path="/mic" element={<MicTest />} />
        <Route path="/share" element={<B2Bconnect />} />
        <Route path="/ai-resource-finder" element={<AIResourceFinder />} />
        <Route path="/set-schedule" element={<SetSchedule />} />
        <Route path="/set-schedule2" element={<SetSchedule2 />} />
        <Route path="/smart-resources" element={<SmartResources />} />
        <Route path="/structured-breakdown" element={<StructuredBreakdown />} />
        <Route path="/video-section" element={<VideoSection />} />
        <Route path="/smart-resources" element={<SmartResources />} />
        <Route path="/copy-check" element={<QuestionUploadComponent />} />
        <Route path="/handwritten-test" element={<HandwrittenTest />} />
        <Route path="/smart-resources" element={<SmartResources />} />
        <Route path="/copy-check" element={<QuestionUploadComponent />} />
        <Route path="/ai-finder" element={<AIFinder />} />
        <Route path="/resource-notes" element={<ResourceNotes />} />
        <Route path="/lock-pop" element={< ContestPopup />} />
        <Route path="/structured-breakdown2" element={<StructuredBreakdown2 />} />
        <Route path="/viva" element={<Viva />} />
        <Route path="/notes-pdf" element={<NotesPdf />} />

      </Routes >
    </LanguageProvider >
  );
}
