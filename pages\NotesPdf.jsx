import { useState, useEffect } from 'react';
import {
    Menu,
    Download,
    Printer,
    MoreVertical,
    FileText,
    Sparkles
} from 'lucide-react';

const NotesPdf = () => {
    const [selectedPdf, setSelectedPdf] = useState(0);
    const [pdfData, setPdfData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Fetch PDF data - replace with actual API call
    useEffect(() => {
        const fetchPdfData = async () => {
            try {
                setLoading(true);
                setError(null);

                // Simulate API call - replace with actual API endpoint
                const mockData = {
                    "PDF_Links_Data": [
                        "https://cbseacademic.nic.in/web_material/CurriculumMain26/SrSec/Chemistry_SrSec_2025-26.pdf",
                        "http://stjohnsschoolkota.edu.in/DocTypes/Documents/XII_Chemistry_Practical_2024_25.pdf",
                        "https://cdn1.byjus.com/wp-content/uploads/2023/04/CBSE-Class-12-Chemistry-Syllabus-2023-24.pdf",
                        "https://wisdominstitutions.com/wp-content/uploads/2020/09/null-1.pdf"
                    ]
                };

                // Simulate network delay
                await new Promise(resolve => setTimeout(resolve, 1000));

                setPdfData(mockData.PDF_Links_Data);

                // Auto-select first PDF if available
                if (mockData.PDF_Links_Data.length > 0) {
                    setSelectedPdf(0);
                }
            } catch (err) {
                setError('Failed to load PDF data');
                console.error('Error fetching PDF data:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchPdfData();
    }, []);

    const handlePdfSelect = (index) => {
        setSelectedPdf(index);
    };

    const handleDownload = () => {
        if (pdfData[selectedPdf]) {
            const link = document.createElement('a');
            link.href = pdfData[selectedPdf];
            link.download = `PDF-${selectedPdf + 1}.pdf`;
            link.click();
        }
    };

    const handlePrint = () => {
        if (pdfData[selectedPdf]) {
            window.open(pdfData[selectedPdf], '_blank');
        }
    };

    const handleAiSmartNotes = () => {
        // Placeholder for AI Smart Notes functionality
        alert('AI Smart Notes feature coming soon!');
    };

    const getPdfFileName = (index) => {
        return `PDF - ${index + 1}`;
    };

    return (
        <div className="flex h-screen bg-gray-100">
            {/* Left Sidebar */}
            <div className="w-80 bg-white shadow-lg flex flex-col">
                {/* Header */}
                <div className="p-6 text-center">
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
                        My Resources
                    </h1>
                    <hr className="border-gray-200 mb-4" />

                    {/* Day Info */}
                    <div className="flex items-center gap-4 mb-6">
                        <span className="text-lg font-medium text-gray-800">Day 1</span>
                        <span className="text-gray-500">Monday, June 5</span>
                    </div>
                </div>

                {/* Notes Section */}
                {/* <div className="px-6 mb-4">
                    <div className="flex items-center gap-4 p-3 rounded-xl bg-gray-50">
                        <div className="text-gray-600">
                            <FileText className="w-5 h-5" />
                        </div>
                        <span className="text-xl text-gray-700">Notes</span>
                    </div>
                </div> */}

                {/* PDF List */}
                <div className="flex-1 px-6 space-y-2">
                    {loading ? (
                        <div className="space-y-2">
                            {[1, 2, 3, 4].map((i) => (
                                <div key={i} className="animate-pulse">
                                    <div className="flex items-center gap-4 p-3 border border-gray-200 rounded">
                                        <div className="w-5 h-5 bg-gray-300 rounded"></div>
                                        <div className="h-4 bg-gray-300 rounded w-16"></div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        pdfData.map((_, index) => (
                            <div
                                key={index}
                                onClick={() => handlePdfSelect(index)}
                                className={`flex items-center gap-4 p-3 border rounded cursor-pointer transition-all duration-200 ${selectedPdf === index
                                    ? 'bg-gray-100 border-gray-300'
                                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                    }`}
                            >
                                <div className="text-purple-600">
                                    <FileText className="w-5 h-5" />
                                </div>
                                <span className={`font-medium ${selectedPdf === index ? 'text-blue-600' : 'text-gray-700'
                                    }`}>
                                    {getPdfFileName(index)}
                                </span>
                            </div>
                        ))
                    )}
                </div>

                {/* AI Smart Notes Button */}
                {/* <div className="p-6">
                    <button
                        onClick={handleAiSmartNotes}
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg py-3 px-4 flex items-center justify-center gap-3 hover:from-blue-600 hover:to-purple-700 transition-all duration-200"
                    >
                        <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                            <Sparkles className="w-4 h-4 text-blue-500" />
                        </div>
                        <span className="font-medium text-lg">AI Smart Notes</span>
                    </button>
                </div> */}
            </div>

            {/* Main Content Area */}
            <div className="flex-1 flex flex-col">
                {/* PDF Header */}


                {/* PDF Viewer */}
                <div className="flex-1 bg-gray-300 overflow-hidden">
                    {loading ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                                <p className="text-gray-600">Loading PDFs...</p>
                            </div>
                        </div>
                    ) : error ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center text-red-500">
                                <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
                                <p className="text-lg font-medium">Error Loading PDFs</p>
                                <p className="text-sm">{error}</p>
                                <button
                                    onClick={() => window.location.reload()}
                                    className="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                >
                                    Retry
                                </button>
                            </div>
                        </div>
                    ) : pdfData.length > 0 ? (
                        <iframe
                            src={pdfData[selectedPdf]}
                            className="w-full h-full border-0"
                            title={`PDF Viewer - ${getPdfFileName(selectedPdf)}`}
                            onLoad={() => console.log('PDF loaded')}
                            onError={() => console.error('PDF failed to load')}
                        />
                    ) : (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center text-gray-500">
                                <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
                                <p className="text-lg">No PDFs Available</p>
                                <p className="text-sm">No PDF resources found for this day</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default NotesPdf;