import React, { useRef, useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

const KEYBOARD_IMAGE_URL = "https://gradding.s3.ap-south-1.amazonaws.com/website-images/uploads/pages/66bc348870739-writing-task-1-process-chart-recycling-bottles.jpg";

// AI Header Generation Function
const generateHeaderFromContent = async (content) => {
    try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ********************************************************************************************************************************************************************`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: "system",
                        content: "You are a helpful assistant that generates concise, descriptive titles for educational content. Respond with just the title (no quotes or formatting). Keep it under 7 words."
                    },
                    {
                        role: "user",
                        content: `Generate a short, descriptive title for this content:\n\n${content}`
                    }
                ],
                temperature: 0,
                max_tokens: 20
            }),
        });

        const data = await response.json();
        return data.choices?.[0]?.message?.content.trim() || "Resource Notes";
    } catch (error) {
        console.error("Error generating header:", error);
        return "Resource Notes";
    }
};

const SummaryContent = ({ summary }) => {
    if (!summary || !summary.contentBlocks) {
        return <p className="text-gray-500">Summary not available.</p>;
    }

    return (
        <div className="text-gray-800 markdown-body">
            {summary.contentBlocks.map((block, idx) => (
                <div key={idx} className="mb-6 last:mb-0">
                    {block.heading && (
                        <h2 className="text-2xl font-semibold mb-3 text-slate-700">
                            <ReactMarkdown remarkPlugins={[remarkGfm, remarkMath]} rehypePlugins={[rehypeKatex]}>
                                {block.heading}
                            </ReactMarkdown>
                        </h2>
                    )}
                    {block.paragraphs && block.paragraphs.map((p, pIdx) => (
                        <div key={pIdx} className="text-slate-600 mb-3 leading-relaxed">
                            <ReactMarkdown remarkPlugins={[remarkGfm, remarkMath]} rehypePlugins={[rehypeKatex]}>
                                {p}
                            </ReactMarkdown>
                        </div>
                    ))}
                    {block.list && (
                        <ul className="text-slate-600 space-y-1 pl-5">
                            {block.list.map((item, lIdx) => (
                                <li key={lIdx} className="flex items-start">
                                    <span className="mr-2">•</span>
                                    <span className="flex-1">
                                        <ReactMarkdown remarkPlugins={[remarkGfm, remarkMath]} rehypePlugins={[rehypeKatex]}>
                                            {item}
                                        </ReactMarkdown>
                                    </span>
                                </li>
                            ))}
                        </ul>
                    )}
                </div>
            ))}
        </div>
    );
};

const ImageContent = ({ image }) => {
    if (!image) return null;

    return (
        <div className="mt-4 md:mt-0">
            {image.url ? (
                <>
                    <img
                        src={image.url}
                        alt={image.alt || "Note image"}
                        className="w-full h-auto rounded-lg border border-gray-200"
                    />
                    {image.caption && <p className="text-xs text-gray-500 mt-2 text-center italic">{image.caption}</p>}
                </>
            ) : (
                <div className="w-full h-64 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                    <p className="text-gray-500">Generating image...</p>
                </div>
            )}
        </div>
    );
};

const NotesRenderer = ({ notes }) => {
    if (!notes || notes.length === 0) {
        return <p className="text-center text-gray-500">No notes to display.</p>;
    }

    return (
        <div className="space-y-10">
            {notes.map(([summaryChunk, imageLinkChunk], index) => (
                <div
                    key={index}
                    className="p-6 bg-white border border-gray-200 rounded-xl shadow-lg"
                >
                    <div className="grid grid-cols-1 md:grid-cols-12 gap-x-8 gap-y-6 items-start">
                        <div className={imageLinkChunk ? 'md:col-span-7 lg:col-span-8' : 'md:col-span-12'}>
                            <SummaryContent summary={summaryChunk} />
                        </div>
                        {imageLinkChunk && (
                            <div className="md:col-span-5 lg:col-span-4">
                                <ImageContent image={imageLinkChunk} />
                            </div>
                        )}
                    </div>
                </div>
            ))}
        </div>
    );
};

const generateImageFromPrompt = async (prompt) => {
    try {
        const response = await fetch('https://image-generator-optimized-0f53f35-v4.app.beam.cloud', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer cpxjIHGyDUggeCZSEgd7TSs_xuIaJLxQyplSlPcpEv35qftljIUmetr9Drtj_MUyC9PUSJLvV1vbjljWohB8Sw=='
            },
            body: JSON.stringify({ prompt })
        });
        console.log('image gen response', response);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.image;
    } catch (error) {
        console.error("Error generating image:", error);
        return null;
    }
};


const ResourceNotes = () => {
    const contentRef = useRef(null);
    const [notesData, setNotesData] = useState({
        normal_notes: [
            [
                {
                    contentBlocks: [
                        {
                            heading: "What is Machine Learning?",
                            paragraphs: [
                                "Machine learning is a branch of *artificial intelligence (AI)* and computer science which focuses on the use of data and algorithms to imitate the way that humans learn, gradually improving its accuracy.\n\nIBM has a simpler definition: \n> 'Machine learning is a type of artificial intelligence that allows software applications to become more accurate at predicting outcomes without being explicitly programmed to do so.'"
                            ]
                        },
                        {
                            heading: "Key Concepts",
                            list: [
                                "*Supervised Learning*: The algorithm learns from labeled training data.",
                                "*Unsupervised Learning*: The algorithm learns patterns from unlabeled data.",
                                "*Reinforcement Learning*: The algorithm learns by trial and error using feedback from its actions.",
                                "*Features*: The input variables used to make predictions.",
                                "*Labels*: The output variables we're trying to predict."
                            ]
                        }
                    ]
                },
                {
                    url: KEYBOARD_IMAGE_URL,
                    alt: "Trophy Image",
                    caption: "An illustrative trophy icon."
                }
            ]
        ]
    });
    const [pageTitle, setPageTitle] = useState("Loading Notes...");
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [transcript, setTranscript] = useState('');
    const [ocrData, setOcrData] = useState('');
    const [ytData, setYtData] = useState([]);

    useEffect(() => {
        const fetchTranscription = async () => {
            setIsLoading(true);
            setError(null);

            const url = 'https://py.aiclassroom.in/get-transcription-from_YouTube/';

            try {
                const response = await axios.post(url, {
                    youtube_url: localStorage.getItem("ytLinkForNotes")
                });

                console.log("Fetching transcription for video:", localStorage.getItem("ytLinkForNotes"));

                setTranscript(response.data.Transcription_Data || 'No transcript available.');
                console.log("Transcription response:", response.data);
            } catch (error) {
                console.error('Error fetching transcript:', error);
                setTranscript('Unable to load transcript. Please try again later.');
            } finally {
                setIsLoading(false);
            }

        }
        fetchTranscription();


        const fetchOcrData = async () => {
            setIsLoading(true);
            setError(null);

            const url = 'https://py.aiclassroom.in/get-ocr-from_YouTube/';

            try {
                const response = await axios.post(url, {
                    youtube_url: localStorage.getItem("ytLinkForNotes")
                });

                console.log("Fetching OCR data for video:", localStorage.getItem("ytLinkForNotes"));

                setOcrData(response.data.OCR_DATA || 'No OCR data available.');
                console.log("OCR data response:", response.data);
            } catch (error) {
                console.error('Error fetching OCR data:', error);
                setOcrData('Unable to load OCR data. Please try again later.');
            } finally {
                setIsLoading(false);
            }

        }
        // fetchOcrData();

    });

    const BackArrowIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-gray-700">
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
        </svg>
    );

    const DownloadIcon = () => (
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 mr-2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
        </svg>
    );

    useEffect(() => {
        const fetchNotes = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const response = await fetch('https://py.aiclassroom.in/make-notes-from_OCR/', { //NGROK URL
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        "ocr_data": "[This topic explores rotational dynamics and center of mass in systems of particles. It dives into the complexities of rigid body motion, discussing key concepts like angular momentum, torque, and equilibrium. The study emphasizes how understanding motion around fixed axes and center of mass plays a vital role in analyzing various systems. Building upon the foundation of individual particles, we now shift our attention to the intricacies of systems of particles moving in real-world scenarios. This chapter expands our focus from individual particles to systems of particles in motion, acknowledging that real-world objects have finite size. Unlike the simplistic point mass model, extended bodies require a deeper understanding involving multiple interconnected particles. The discussion aims to grasp the complexities of rotational motion within such systems. The study of motion in physical systems involves understanding the motion of a group of particles together, focusing on the center of mass. By considering bodies as rigid maintaining a constant shape despite being theoretically unattainable many mechanical problems can be effectively addressed, even though real bodies do deform slightly. Rigid bodies like wheels or planets don't visibly deform during motion. They are treated as solid objects. They can have various motions like linear or rotational. Understanding concepts like center of mass, linear momentum, angular velocity, torque, and equilibrium helps in studying their movement effectively. This section delves into rotational dynamics about a fixed axis and angular momentum in such scenarios. It contrasts pure translational motion where all body particles share the same velocity with rolling motion of a rigid body (like a cylinder) sliding on an inclined plane. Key points examine how particles move collectively in translation versus rotation. A cylinder on an inclined plane seems to move straight down, but its particles don't all move uniformly, showing more than just translation. Constrained rigid bodies without translation can only rotate, with a fixed axis called the axis of rotation. This rotation is the 'something else' beyond simple translation. Rotation about a fixed axis is common in things like fans, wheels, and merry-go-rounds. When a rigid object rotates around an axis, all points move at the same speed. Unlike sliding motion (Fig. 6.1), rolling motion (Fig. 6.2) features points moving at different speeds simultaneously. In rotational motion, every particle of a rigid body moves in a circle perpendicular to the axis. For example, if a cylinder rolls without slipping, the point of contact]",
                        "desired_language": "english"
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.Notes_Data?.normal_notes && data.Notes_Data.normal_notes.length > 0) {
                    // Generate title from first 200 characters of content
                    const firstNoteContent = data.Notes_Data.normal_notes[0].substring(0, 200);
                    const generatedTitle = await generateHeaderFromContent(firstNoteContent);
                    setPageTitle(generatedTitle);
                } else {
                    setPageTitle("Resource Notes");
                }

                // First create the notes structure with empty image placeholders
                const transformedData = {
                    normal_notes: data.Notes_Data.normal_notes.map(note => {
                        const contentBlocks = [];
                        let currentHeading = "";
                        let currentParagraphs = [];
                        let currentList = [];

                        const lines = note.split('\n');
                        for (const line of lines) {
                            if (line.startsWith('### ')) {
                                if (currentHeading || currentParagraphs.length > 0 || currentList.length > 0) {
                                    contentBlocks.push({
                                        heading: currentHeading,
                                        paragraphs: currentParagraphs.filter(p => p !== ''),
                                        list: currentList.length > 0 ? currentList : undefined,
                                    });
                                }
                                currentHeading = line.substring(4).trim();
                                currentParagraphs = [];
                                currentList = [];
                            } else if (line.startsWith('* ') || line.startsWith('- ')) {
                                currentList.push(line.substring(2).trim());
                            } else if (line.trim() === '') {
                                if (currentParagraphs.length > 0 && currentList.length > 0) {
                                    contentBlocks.push({
                                        heading: currentHeading,
                                        paragraphs: currentParagraphs.filter(p => p !== ''),
                                        list: currentList.length > 0 ? currentList : undefined,
                                    });
                                    currentHeading = "";
                                    currentParagraphs = [];
                                    currentList = [];
                                } else if (currentParagraphs.length > 0) {
                                    currentParagraphs.push(line.trim());
                                } else if (currentList.length > 0) {
                                    contentBlocks.push({
                                        heading: currentHeading,
                                        paragraphs: currentParagraphs.filter(p => p !== ''),
                                        list: currentList.length > 0 ? currentList : undefined,
                                    });
                                    currentHeading = "";
                                    currentParagraphs = [];
                                    currentList = [];
                                }
                            } else {
                                if (currentHeading === "" && contentBlocks.length === 0) {
                                    currentParagraphs.push(line.trim());
                                } else if (currentList.length > 0) {
                                    contentBlocks.push({
                                        heading: currentHeading,
                                        paragraphs: currentParagraphs.filter(p => p !== ''),
                                        list: currentList.length > 0 ? currentList : undefined,
                                    });
                                    currentHeading = "";
                                    currentParagraphs = [line.trim()];
                                    currentList = [];
                                } else {
                                    currentParagraphs.push(line.trim());
                                }
                            }
                        }

                        if (currentHeading || currentParagraphs.length > 0 || currentList.length > 0) {
                            contentBlocks.push({
                                heading: currentHeading,
                                paragraphs: currentParagraphs.filter(p => p !== ''),
                                list: currentList.length > 0 ? currentList : undefined,
                            });
                        }

                        if (contentBlocks.length > 0 && !contentBlocks[0].heading && note.split('\n')[0].trim() === contentBlocks[0].paragraphs[0]) {
                            const firstLineAsHeading = contentBlocks[0].paragraphs[0];
                            contentBlocks[0] = {
                                heading: firstLineAsHeading,
                                paragraphs: contentBlocks[0].paragraphs.slice(1).filter(p => p !== ''),
                                list: contentBlocks[0].list,
                            };
                        }

                        return [
                            { contentBlocks: contentBlocks },
                            { url: null, alt: null, caption: null } // Empty image placeholder
                        ];
                    })
                };

                // Set the initial data with empty image placeholders
                setNotesData(transformedData);
                console.log("Transformed Notes Data:", transformedData);
                setIsLoading(false);
                console.log(transformedData);

                // Now generate images for each note and update them one by one
                const updatedNotes = await Promise.all(transformedData.normal_notes.map(async ([contentBlock]) => {
                    try {
                        const imagePrompt = contentBlock.contentBlocks[0]?.heading ||
                            contentBlock.contentBlocks[0]?.paragraphs?.[0] ||
                            "Educational concept";
                        const generatedImageUrl = await generateImageFromPrompt(imagePrompt);

                        return [
                            contentBlock,
                            generatedImageUrl ? {
                                url: generatedImageUrl,
                                alt: "Generated illustration",
                                caption: "AI generated illustration"
                            } : null
                        ];
                    } catch (err) {
                        console.error("Error generating image:", err);
                        return [
                            contentBlock,
                            {
                                url: KEYBOARD_IMAGE_URL,
                                alt: "Fallback image",
                                caption: "Illustration"
                            }
                        ];
                    }
                }));

                // Update the state with the generated images
                setNotesData({
                    normal_notes: updatedNotes
                });
            } catch (err) {
                console.error("Error fetching notes:", err);
                setError(err.message);
                setPageTitle("Error Loading Notes");
            } finally {
                setIsLoading(false);
            }
        };

        fetchNotes();
    }, []);

    const handleDownloadPdf = async () => {
        const input = contentRef.current;
        if (!input) {
            console.error("Content ref is null. Cannot generate PDF.");
            alert("Failed to generate PDF: Content not found.");
            return;
        }

        const style = document.createElement('style');
        style.textContent = `
      * {
        color: inherit !important;
        background-color: inherit !important;
        border-color: inherit !important;
      }
      img { display: initial; }
      .markdown-body pre {
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      .katex {
        font-size: 1.2em;
      }
      .katex-display {
        margin-top: 1em;
        margin-bottom: 1em;
        overflow-x: auto;
      }
    `;
        document.head.appendChild(style);

        const elementsToHide = input.querySelectorAll('.no-print');
        elementsToHide.forEach(el => el.style.visibility = 'hidden');

        try {
            const images = input.querySelectorAll('img');
            const imageLoadPromises = Array.from(images).map(img => {
                if (img.complete && img.naturalWidth > 0) return Promise.resolve();
                return new Promise((resolve, reject) => {
                    img.onload = async () => {
                        try {
                            if (img.decode) {
                                await img.decode();
                            }
                            if (img.naturalWidth > 0) {
                                resolve();
                            } else {
                                resolve();
                            }
                        } catch (decodeErr) {
                            console.error("Image decode error:", img.src, decodeErr);
                            reject(new Error(`Failed to decode image: ${img.src}`));
                        }
                    };
                    img.onerror = (e) => {
                        console.error("Image loading error:", img.src, e);
                        reject(new Error(`Failed to load image: ${img.src}`));
                    };
                });
            });

            await Promise.all(imageLoadPromises);

            const canvas = await html2canvas(input, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                logging: true,
                ignoreElements: (element) => {
                    return element.classList.contains('no-print');
                },
                onclone: (clonedDoc) => {
                    const pdfStyles = clonedDoc.createElement('style');
                    pdfStyles.textContent = `
            body { font-family: Arial, sans-serif; }
            * { color: #000 !important; }
            .text-slate-600 { color: #475569 !important; }
            .text-slate-700 { color: #334155 !important; }
            .text-slate-800 { color: #1e293b !important; }
            .bg-white { background-color: white !important; }
            .border-gray-200 { border-color: #e5e7eb !important; }
            img { display: initial !important; }
            .markdown-body pre {
              background-color: #f3f4f6 !important;
              padding: 1em !important;
              border-radius: 0.5em !important;
              white-space: pre-wrap !important;
            }
            .markdown-body code {
              background-color: #f3f4f6 !important;
              padding: 0.2em 0.4em !important;
            }
            .markdown-body blockquote {
              border-left: 4px solid #e5e7eb !important;
              padding-left: 1em !important;
            }
            .katex {
              font-size: 1.2em !important;
            }
            .katex-display {
              margin-top: 1em !important;
              margin-bottom: 1em !important;
              overflow-x: auto !important;
            }
            header h1 {
              padding-left: 15px !important;
            }
            header .flex.items-center.space-x-2 {
              margin-top: 15px !important;
              margin-left: 15px !important;
            }
            header p.text-sm {
              padding-left: 15px !important;
              margin-top: 5px !important;
            }
          `;
                    clonedDoc.head.appendChild(pdfStyles);
                }
            });

            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF('p', 'mm', 'a4');
            const imgWidth = 210;
            const pageHeight = 297;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;

            let position = 0;

            while (position < imgHeight) {
                pdf.addImage(imgData, 'PNG', 0, -position, imgWidth, imgHeight);
                position += pageHeight;
                if (position < imgHeight) {
                    pdf.addPage();
                }
            }

            pdf.save(`${pageTitle}.pdf`);
        } catch (err) {
            console.error("Error generating PDF:", err);
            let errorMessage = "Failed to generate PDF. Please try again.";
            if (err.message.includes("Failed to load image") || err.message.includes("has no natural dimensions")) {
                errorMessage = "Failed to generate PDF: Image loading issue. Check console for details.";
            } else if (err.message.includes("unsupported color function")) {
                errorMessage = "Failed to generate PDF: There's an unsupported CSS color format.";
            }
            alert(errorMessage);
        } finally {
            elementsToHide.forEach(el => el.style.visibility = '');
            style.remove();
        }
    };

    return (
        <div className="min-h-screen bg-slate-50 p-4 sm:p-8">
            <style>{`
        .markdown-body {
          line-height: 1.6;
        }
        .markdown-body h1, .markdown-body h2, .markdown-body h3 {
          margin-top: 1em;
          margin-bottom: 0.5em;
          font-weight: 600;
        }
        .markdown-body p {
          margin-bottom: 1em;
        }
        .markdown-body ul, .markdown-body ol {
          margin-bottom: 1em;
        }
        .markdown-body code {
          background-color: #f3f4f6;
          padding: 0.2em 0.4em;
          border-radius: 0.25em;
          font-family: monospace;
        }
        .markdown-body pre {
          background-color: #f3f4f6;
          padding: 1em;
          border-radius: 0.5em;
          overflow-x: auto;
        }
        .markdown-body a {
          color: #3b82f6;
          text-decoration: underline;
        }
        .markdown-body blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 1em;
          margin-left: 0;
          color: #6b7280;
        }
        .katex {
          font-size: 1.2em;
        }
        .katex-display {
          display: block;
          overflow-x: auto;
          padding: 1em 0;
          margin: 1em 0;
        }
      `}</style>

            <div ref={contentRef} className="max-w-6xl mx-auto">
                <header className="mb-8">
                    <div className="flex justify-between items-center mb-4">
                        <button aria-label="Back" className="p-2 rounded-full hover:bg-gray-200 no-print">
                            <BackArrowIcon />
                        </button>
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={handleDownloadPdf}
                                className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 no-print"
                            >
                                <DownloadIcon />
                                Download
                            </button>
                            <div className="flex items-center space-x-2">
                                <img src="/logo.png" alt="AI Classroom Logo" className="h-8 w-8" />
                                <span className="font-semibold text-gray-800">AI Classroom</span>
                            </div>
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold text-slate-800">
                        <ReactMarkdown remarkPlugins={[remarkGfm, remarkMath]} rehypePlugins={[rehypeKatex]}>
                            {pageTitle}
                        </ReactMarkdown>
                    </h1>
                </header>

                {isLoading ? (
                    <div className="text-center py-10">
                        <p className="text-gray-600">Loading notes...</p>
                    </div>
                ) : error ? (
                    <div className="text-center py-10">
                        <p className="text-red-500">Error loading notes: {error}</p>
                        <p className="text-gray-600 mt-2">Showing default notes instead.</p>
                    </div>
                ) : (
                    <NotesRenderer notes={notesData.normal_notes} />
                )}
            </div>
        </div>
    );
};

export default ResourceNotes;